from typing import List

import typer
from omegaconf import OmegaConf
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb, Neo4jGraphDb
from yunfu.db.graph.models import (
    Edge,
    EdgeProp,
    EdgeType,
    NebulaSpace,
    Node,
    NodeProp,
    NodeType,
    Space,
)

from backend.utils import conf

app = typer.Typer(help="图谱数据初始化工具")
console = Console()

nebula_space_name = "yunfu_crop"
NebulaGraphDb._space = NebulaSpace(name=nebula_space_name)


class GraphInitializer:
    """图谱初始化器"""
    space_name = "KGCROP"
    config = {
        "nebula": Config(db=OmegaConf.to_container(conf.nebula)),
        "neo4j": Config(db=OmegaConf.to_container(conf.neo4j)),
    }

    def __init__(self, db_type: str):
        if db_type == "nebula":
            self.graph_db = NebulaGraphDb(self.config["nebula"])
        elif db_type == "neo4j":
            self.graph_db = Neo4jGraphDb(self.config["neo4j"])
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")

    def create_space(self) -> bool:
        """创建图空间"""
        try:
            space = Space(
                name=self.space_name,
                props=[
                    NodeProp(name="name", type="string"),
                    NodeProp(name="age", type="int"),
                    NodeProp(name="occupation", type="string"),
                    NodeProp(name="city", type="string"),
                    NodeProp(name="category", type="string"),
                    NodeProp(name="version", type="string"),
                    NodeProp(name="release_date", type="string"),
                    NodeProp(name="industry", type="string"),
                    NodeProp(name="founded_year", type="int"),
                    NodeProp(name="location", type="string"),
                ],
            )
            console.print(f"[yellow]正在创建图空间: {self.space_name}[/yellow]")
            if self.graph_db.has_space(self.space_name):
                console.print(f"[yellow]图空间 {self.space_name} 已存在，跳过创建[/yellow]")
                self.clear_space()
                return True
            self.graph_db.create_space(space, wait=True)
            console.print(f"[green]✓ 图空间 {self.space_name} 创建成功[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 创建图空间失败: {e}[/red]")
            return False

    def clear_space(self) -> bool:
        """清空图空间"""
        try:
            console.print(f"[yellow]正在清空图空间: {self.space_name}[/yellow]")
            self.graph_db.client.run(self.graph_db.templates.delete_nodes_by_type(self.space_name))
            console.print(f"[green]✓ 图空间 {self.space_name} 清空成功[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 清空图空间失败: {e}[/red]")
            return False

    def create_node_type(self, node_type: NodeType) -> bool:
        """创建节点类型"""
        try:
            graph = self.graph_db.get_graph(self.space_name)
            console.print(f"[yellow]正在创建节点类型: {node_type.name}[/yellow]")
            if graph.has_node_type(node_type.name):
                console.print(f"[yellow]节点类型 {node_type.name} 已存在，跳过创建[/yellow]")
                return True
            graph.create_node_type(node_type, wait=True)
            console.print(f"[green]✓ 节点类型 {node_type.name} 创建成功[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 创建节点类型失败: {e}[/red]")
            return False

    def create_edge_type(self, edge_type: EdgeType) -> bool:
        """创建边类型"""
        try:
            graph = self.graph_db.get_graph(self.space_name)
            console.print(f"[yellow]正在创建边类型: {edge_type.name}[/yellow]")
            if graph.has_edge_type(edge_type.name):
                console.print(f"[yellow]边类型 {edge_type.name} 已存在，跳过创建[/yellow]")
                return True
            graph.create_edge_type(edge_type, wait=True)
            console.print(f"[green]✓ 边类型 {edge_type.name} 创建成功[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 创建边类型失败: {e}[/red]")
            return False

    def get_sample_nodes(self) -> List[Node]:
        """获取示例节点数据"""
        nodes = [
            # 人员节点
            Node(
                id="person_001",
                types=["Person"],
                props={
                    "name": "张三",
                    "age": 30,
                    "occupation": "软件工程师",
                    "city": "北京"
                }
            ),
            Node(
                id="person_002",
                types=["Person"],
                props={
                    "name": "李四",
                    "age": 28,
                    "occupation": "产品经理",
                    "city": "上海"
                }
            ),
            Node(
                id="person_003",
                types=["Person"],
                props={
                    "name": "王五",
                    "age": 35,
                    "occupation": "数据科学家",
                    "city": "深圳"
                }
            ),
            # 公司节点
            Node(
                id="company_001",
                types=["Company"],
                props={
                    "name": "云服科技",
                    "industry": "人工智能",
                    "founded_year": 2015,
                    "location": "北京"
                }
            ),
            Node(
                id="company_002",
                types=["Company"],
                props={
                    "name": "数据智能",
                    "industry": "大数据",
                    "founded_year": 2018,
                    "location": "上海"
                }
            ),
            # 产品节点
            Node(
                id="product_001",
                types=["Product"],
                props={
                    "name": "知识图谱平台",
                    "category": "AI平台",
                    "version": "2.0",
                    "release_date": "2023-01-01"
                }
            ),
            Node(
                id="product_002",
                types=["Product"],
                props={
                    "name": "数据分析工具",
                    "category": "分析工具",
                    "version": "1.5",
                    "release_date": "2023-06-01"
                }
            )
        ]
        return nodes

    def get_node_types(self) -> List[NodeType]:
        """获取节点类型"""
        node_types = [
            NodeType(name="Person"),
            NodeType(name="Company"),
            NodeType(name="Product"),
            NodeType(name="c")
        ]
        return node_types

    def get_edge_types(self) -> List[EdgeType]:
        """获取边类型"""
        edge_types = [
            EdgeType(
                name="WORKS_FOR",
                props=[
                    EdgeProp(name="position", type="string"),
                    EdgeProp(name="start_date", type="string"),
                    EdgeProp(name="department", type="string"),
                    EdgeProp(name="c", type="bool"),
                    EdgeProp(name="id", type="string"),
                ]
            ),
            EdgeType(
                name="DEVELOPS",
                props=[
                    EdgeProp(name="role", type="string"),
                    EdgeProp(name="start_date", type="string"),
                    EdgeProp(name="c", type="bool"),
                    EdgeProp(name="id", type="string"),
                ]
            ),
            EdgeType(
                name="COLLABORATES_WITH",
                props=[
                    EdgeProp(name="project", type="string"),
                    EdgeProp(name="start_date", type="string"),
                    EdgeProp(name="c", type="bool"),
                    EdgeProp(name="id", type="string"),
                ]
            ),
            EdgeType(
                name="USES",
                props=[
                    EdgeProp(name="usage_type", type="string"),
                    EdgeProp(name="frequency", type="string"),
                    EdgeProp(name="c", type="bool"),
                    EdgeProp(name="id", type="string"),
                ]
            )
        ]
        return edge_types

    def get_sample_edges(self) -> List[Edge]:
        """获取示例边数据"""
        edges = [
            # 工作关系
            Edge(
                src_id="person_001",
                dst_id="company_001",
                type="WORKS_FOR",
                props={
                    "position": "高级工程师",
                    "start_date": "2020-01-01",
                    "department": "研发部"
                }
            ),
            Edge(
                src_id="person_002",
                dst_id="company_001",
                type="WORKS_FOR",
                props={
                    "position": "产品经理",
                    "start_date": "2021-03-01",
                    "department": "产品部"
                }
            ),
            Edge(
                src_id="person_003",
                dst_id="company_002",
                type="WORKS_FOR",
                props={
                    "position": "数据科学家",
                    "start_date": "2019-06-01",
                    "department": "算法部"
                }
            ),
            # 产品开发关系
            Edge(
                src_id="company_001",
                dst_id="product_001",
                type="DEVELOPS",
                props={
                    "role": "主要开发商",
                    "start_date": "2022-01-01"
                }
            ),
            Edge(
                src_id="company_002",
                dst_id="product_002",
                type="DEVELOPS",
                props={
                    "role": "主要开发商",
                    "start_date": "2022-06-01"
                }
            ),
            # 人员协作关系
            Edge(
                src_id="person_001",
                dst_id="person_002",
                type="COLLABORATES_WITH",
                props={
                    "project": "知识图谱项目",
                    "start_date": "2022-01-01"
                }
            ),
            # 产品使用关系
            Edge(
                src_id="person_003",
                dst_id="product_001",
                type="USES",
                props={
                    "usage_type": "日常工作",
                    "frequency": "每日"
                }
            )
        ]
        return edges

    def import_nodes(self, nodes: List[Node]) -> bool:
        """导入节点数据"""
        try:
            graph = self.graph_db.get_graph(self.space_name)

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("正在导入节点...", total=len(nodes))

                for node in nodes:
                    graph.insert(node)
                    progress.advance(task)

            console.print(f"[green]✓ 成功导入 {len(nodes)} 个节点[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 导入节点失败: {e}[/red]")
            return False

    def import_edges(self, edges: List[Edge]) -> bool:
        """导入边数据"""
        try:
            graph = self.graph_db.get_graph(self.space_name)

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("正在导入边...", total=len(edges))

                for edge in edges:
                    graph.insert(edge)
                    progress.advance(task)

            console.print(f"[green]✓ 成功导入 {len(edges)} 条边[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ 导入边失败: {e}[/red]")
            return False


@app.command()
def init_nebula():
    """初始化 Nebula 图数据库"""
    console.print("[bold blue]开始初始化 Nebula 图数据库...[/bold blue]")

    try:
        initializer = GraphInitializer("nebula")

        # 创建图空间
        if not initializer.create_space():
            raise typer.Exit(1)

        # 创建节点类型
        node_types = initializer.get_node_types()
        for node_type in node_types:
            if not initializer.create_node_type(node_type):
                raise typer.Exit(1)

        # 创建边类型
        edge_types = initializer.get_edge_types()
        for edge_type in edge_types:
            if not initializer.create_edge_type(edge_type):
                raise typer.Exit(1)

        # 导入节点
        nodes = initializer.get_sample_nodes()
        if not initializer.import_nodes(nodes):
            raise typer.Exit(1)

        # 导入边
        edges = initializer.get_sample_edges()
        if not initializer.import_edges(edges):
            raise typer.Exit(1)

        console.print("[bold green]✓ Nebula 图数据库初始化完成！[/bold green]")

    except Exception as e:
        console.print(f"[bold red]✗ 初始化失败: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def init_neo4j():
    """初始化 Neo4j 图数据库"""
    console.print("[bold blue]开始初始化 Neo4j 图数据库...[/bold blue]")

    try:
        initializer = GraphInitializer("neo4j")

        # Neo4j 不需要显式创建空间，使用标签来区分
        # 导入节点
        nodes = initializer.get_sample_nodes()
        # 为 Neo4j 添加空间标签
        for node in nodes:
            node.types.append(GraphInitializer.space_name)
            node.types.append("c")

        if not initializer.import_nodes(nodes):
            raise typer.Exit(1)

        # 导入边
        edges = initializer.get_sample_edges()
        if not initializer.import_edges(edges):
            raise typer.Exit(1)

        console.print("[bold green]✓ Neo4j 图数据库初始化完成！[/bold green]")

    except Exception as e:
        console.print(f"[bold red]✗ 初始化失败: {e}[/bold red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()