spark:
  master: spark://yftool-db-spark:7077
  # master: spark://*************:13001
  app_name: crop
  config:
    max_to_string_fields: 100
    jars: /opt/yunfu/yfproduct/yfkm/services/kg/crop/jars/neo4j-connector-apache-spark_2.12-5.0.1_for_spark_3.jar,/opt/yunfu/yfproduct/yfkm/services/kg/crop/jars/graphframes-0.8.2-spark3.2-s_2.12.jar
  neo4j:
    format: org.neo4j.spark.DataSource
    url: bolt://yfproduct-yfkm-web-neo4j:7687
    username: neo4j
    password: yunfu2017
neo4j:
  schema: bolt
  host: yfproduct-yfkm-web-neo4j
  port: 7687
  username: neo4j
  password: yunfu2017
nebula:
  host: *************
  port: 9669
  username: root
  password: root
notice:
  url: http://yfproduct-yfkm-backend-api:8000/api/kg/crop_complete/
  timeout: 600
